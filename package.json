{"name": "turkish-learning-app", "version": "1.0.0", "description": "A comprehensive Turkish language learning application with Istanbul Book Curriculum Integration", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ai\"", "dev:backend": "cd backend && npm run start:dev", "dev:frontend": "cd frontend && npm run dev", "dev:admin": "cd admin && npm run dev", "dev:ai": "cd ai-service && uvicorn main:app --reload", "build": "npm run build:backend && npm run build:frontend && npm run build:admin", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:admin": "cd admin && npm run build", "test": "npm run test:backend && npm run test:frontend && npm run test:ai", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:ai": "cd ai-service && python -m pytest", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:build": "docker-compose build", "setup": "npm run setup:backend && npm run setup:frontend && npm run setup:admin && npm run setup:mobile", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "setup:admin": "cd admin && npm install", "setup:mobile": "cd mobile && npm install", "setup:ai": "cd ai-service && pip install -r requirements.txt", "db:migrate": "cd backend && npm run migration:run", "db:seed": "cd backend && npm run seed", "clean": "npm run clean:backend && npm run clean:frontend && npm run clean:admin", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:frontend": "cd frontend && rm -rf .next node_modules", "clean:admin": "cd admin && rm -rf .next node_modules"}, "keywords": ["turkish", "language-learning", "education", "ai", "nlp", "nextjs", "<PERSON><PERSON><PERSON>", "react-native"], "author": "Turkish Learning App Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "workspaces": ["backend", "frontend", "admin", "mobile"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/turkish-learning-app.git"}, "bugs": {"url": "https://github.com/your-username/turkish-learning-app/issues"}, "homepage": "https://github.com/your-username/turkish-learning-app#readme", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.19.2", "lucide-react": "^0.525.0", "tailwind-merge": "^3.3.1"}}