import { UserRole, CEFRLevel } from '@/shared/types';
import { UserPreferences } from './user-preferences.entity';
import { UserProgress } from '../../progress/entities/user-progress.entity';
import { ExerciseAttempt } from '../../progress/entities/exercise-attempt.entity';
import { UserAchievement } from '../../gamification/entities/user-achievement.entity';
import { ReviewItem } from '../../progress/entities/review-item.entity';
import { Notification } from '../../common/entities/notification.entity';
import { UserSession } from '../../auth/entities/user-session.entity';
export declare class User {
    id: string;
    email: string;
    username: string;
    passwordHash: string;
    firstName: string;
    lastName: string;
    profileImage?: string;
    role: UserRole;
    level: CEFRLevel;
    xp: number;
    streak: number;
    lastActivityDate?: Date;
    emailVerified: boolean;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    preferences: UserPreferences;
    progress: UserProgress[];
    exerciseAttempts: ExerciseAttempt[];
    achievements: UserAchievement[];
    reviewItems: ReviewItem[];
    notifications: Notification[];
    sessions: UserSession[];
    get fullName(): string;
}
