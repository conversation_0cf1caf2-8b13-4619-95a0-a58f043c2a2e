"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { MainLayout } from "@/components/layout/main-layout"
import { MultipleChoice } from "@/components/exercises/multiple-choice"
import { FillBlank } from "@/components/exercises/fill-blank"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, ArrowRight, BookOpen, Volume2, CheckCircle, XCircle } from "lucide-react"

// Mock lesson data
const mockLesson = {
  id: "1",
  title: "Basic Greetings",
  description: "Learn how to say hello, goodbye, and introduce yourself in Turkish",
  difficulty: "A1",
  totalSteps: 8,
  currentStep: 1,
  steps: [
    {
      id: "intro",
      type: "introduction",
      title: "Welcome to Basic Greetings",
      content: "In this lesson, you'll learn essential Turkish greetings and how to introduce yourself. These phrases are fundamental for any conversation in Turkish.",
      audioUrl: "/audio/intro-greetings.mp3"
    },
    {
      id: "vocab1",
      type: "vocabulary",
      title: "Common Greetings",
      vocabulary: [
        { turkish: "Merhaba", english: "Hello", pronunciation: "mer-ha-BA", audioUrl: "/audio/merhaba.mp3" },
        { turkish: "Günaydın", english: "Good morning", pronunciation: "gün-ay-DIN", audioUrl: "/audio/gunaydin.mp3" },
        { turkish: "İyi akşamlar", english: "Good evening", pronunciation: "i-yi ak-sham-LAR", audioUrl: "/audio/iyiaksamlar.mp3" },
        { turkish: "Hoşça kal", english: "Goodbye", pronunciation: "hosh-CHA kal", audioUrl: "/audio/hoscakal.mp3" }
      ]
    },
    {
      id: "exercise1",
      type: "multiple-choice",
      question: {
        id: "mc1",
        text: "How do you say 'Hello' in Turkish?",
        difficulty: "A1",
        audioUrl: "/audio/question1.mp3",
        options: [
          { id: "a", text: "Merhaba", isCorrect: true },
          { id: "b", text: "Günaydın", isCorrect: false },
          { id: "c", text: "İyi akşamlar", isCorrect: false },
          { id: "d", text: "Hoşça kal", isCorrect: false }
        ],
        explanation: "Merhaba is the most common way to say hello in Turkish, suitable for any time of day."
      }
    },
    {
      id: "vocab2",
      type: "vocabulary",
      title: "Introductions",
      vocabulary: [
        { turkish: "Benim adım...", english: "My name is...", pronunciation: "be-nim a-DIM", audioUrl: "/audio/benimadim.mp3" },
        { turkish: "Ben...", english: "I am...", pronunciation: "ben", audioUrl: "/audio/ben.mp3" },
        { turkish: "Tanıştığımıza memnun oldum", english: "Nice to meet you", pronunciation: "ta-nish-ti-gi-mi-za mem-NUN ol-dum", audioUrl: "/audio/tanistigimiza.mp3" }
      ]
    },
    {
      id: "exercise2",
      type: "fill-blank",
      question: {
        id: "fb1",
        text: "Complete the introduction: [BLANK] adım Ahmet.",
        difficulty: "A1",
        audioUrl: "/audio/question2.mp3",
        blanks: [
          {
            id: "blank1",
            correctAnswer: "Benim",
            alternatives: ["benim"],
            hint: "This word means 'my' in Turkish"
          }
        ],
        explanation: "Benim adım means 'My name is' in Turkish."
      }
    }
  ]
}

export default function LessonPage() {
  const params = useParams()
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set())
  const [stepResults, setStepResults] = useState<Record<number, boolean>>({})

  const lesson = mockLesson
  const currentStepData = lesson.steps[currentStep]
  const progress = ((currentStep + 1) / lesson.totalSteps) * 100

  const handleNext = () => {
    if (currentStep < lesson.steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepComplete = (success: boolean) => {
    setCompletedSteps(prev => new Set([...prev, currentStep]))
    setStepResults(prev => ({ ...prev, [currentStep]: success }))
    
    // Auto-advance after a short delay
    setTimeout(() => {
      if (currentStep < lesson.steps.length - 1) {
        handleNext()
      }
    }, 1500)
  }

  const renderStep = () => {
    switch (currentStepData.type) {
      case "introduction":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5" />
                <span>{currentStepData.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-lg leading-relaxed">{currentStepData.content}</p>
              {currentStepData.audioUrl && (
                <Button variant="outline" size="sm">
                  <Volume2 className="h-4 w-4 mr-2" />
                  Listen
                </Button>
              )}
              <div className="pt-4">
                <Button onClick={() => handleStepComplete(true)} className="w-full">
                  Continue
                </Button>
              </div>
            </CardContent>
          </Card>
        )

      case "vocabulary":
        return (
          <Card>
            <CardHeader>
              <CardTitle>{currentStepData.title}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                {currentStepData.vocabulary?.map((item, index) => (
                  <div key={index} className="p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="text-xl font-semibold text-turkish-red">{item.turkish}</div>
                        <div className="text-gray-600">{item.english}</div>
                        <div className="text-sm text-gray-500 italic">{item.pronunciation}</div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Volume2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="pt-4">
                <Button onClick={() => handleStepComplete(true)} className="w-full">
                  Continue
                </Button>
              </div>
            </CardContent>
          </Card>
        )

      case "multiple-choice":
        return (
          <MultipleChoice
            question={currentStepData.question}
            onComplete={handleStepComplete}
          />
        )

      case "fill-blank":
        return (
          <FillBlank
            question={currentStepData.question}
            onComplete={handleStepComplete}
          />
        )

      default:
        return <div>Unknown step type</div>
    }
  }

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Lesson Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Lessons
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{lesson.title}</h1>
              <p className="text-muted-foreground">{lesson.description}</p>
            </div>
          </div>
          <Badge variant="secondary">{lesson.difficulty}</Badge>
        </div>

        {/* Progress Bar */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{currentStep + 1} of {lesson.totalSteps}</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {renderStep()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Button 
            variant="outline" 
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            {lesson.steps.map((_, index) => (
              <div
                key={index}
                className={`w-3 h-3 rounded-full ${
                  index === currentStep
                    ? "bg-primary"
                    : completedSteps.has(index)
                    ? stepResults[index]
                      ? "bg-green-500"
                      : "bg-red-500"
                    : "bg-gray-200"
                }`}
              />
            ))}
          </div>

          <Button 
            onClick={handleNext}
            disabled={currentStep === lesson.steps.length - 1}
          >
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>

        {/* Completion Status */}
        {currentStep === lesson.steps.length - 1 && completedSteps.has(currentStep) && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto" />
                <h3 className="text-xl font-semibold text-green-800">Lesson Complete!</h3>
                <p className="text-green-700">Great job! You've completed the Basic Greetings lesson.</p>
                <div className="flex justify-center space-x-4">
                  <Button variant="outline">Review Lesson</Button>
                  <Button>Next Lesson</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
