"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MissingDeleteDateColumnError = void 0;
const TypeORMError_1 = require("./TypeORMError");
class MissingDeleteDateColumnError extends TypeORMError_1.TypeORMError {
    constructor(entityMetadata) {
        super(`Entity "${entityMetadata.name}" does not have delete date columns.`);
    }
}
exports.MissingDeleteDateColumnError = MissingDeleteDateColumnError;

//# sourceMappingURL=MissingDeleteDateColumnError.js.map
