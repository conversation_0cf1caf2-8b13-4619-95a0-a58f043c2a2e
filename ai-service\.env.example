# Environment Configuration
ENVIRONMENT=development
DEBUG=true
PORT=8000

# Database Configuration
DATABASE_URL=postgresql://turkish_user:turkish_password@localhost:5432/turkish_learning_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800

# Speech Processing Configuration
SPEECH_MODEL=whisper-1

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
