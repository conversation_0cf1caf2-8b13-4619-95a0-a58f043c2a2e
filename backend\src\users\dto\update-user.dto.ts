import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  firstName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  lastName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  profileImage?: string;
}

export class UpdatePreferencesDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  theme?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  emailNotifications?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  pushNotifications?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  reminderNotifications?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  achievementNotifications?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  dailyXpGoal?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  weeklyLessonGoal?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  targetLevel?: string;
}
