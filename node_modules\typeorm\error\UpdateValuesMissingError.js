"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateValuesMissingError = void 0;
const TypeORMError_1 = require("./TypeORMError");
class UpdateValuesMissingError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Cannot perform update query because update values are not defined. Call "qb.set(...)" method to specify updated values.`);
    }
}
exports.UpdateValuesMissingError = UpdateValuesMissingError;

//# sourceMappingURL=UpdateValuesMissingError.js.map
