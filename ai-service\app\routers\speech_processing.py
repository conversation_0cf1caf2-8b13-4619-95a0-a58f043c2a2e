from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import aiofiles
import os
import uuid
from typing import Optional

from app.core.config import settings

router = APIRouter()

@router.post("/transcribe")
async def transcribe_audio(
    audio: UploadFile = File(...),
    language: str = "tr"  # Turkish by default
):
    """Transcribe audio to text using speech recognition"""
    
    # Validate file type
    allowed_audio_types = ['.mp3', '.wav', '.m4a', '.ogg', '.flac']
    filename = audio.filename or "unknown"
    file_extension = os.path.splitext(filename)[1].lower()

    if file_extension not in allowed_audio_types:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported audio format. Allowed: {allowed_audio_types}"
        )

    # Validate file size (max 25MB for audio)
    max_audio_size = 25 * 1024 * 1024  # 25MB
    file_size = audio.size or 0
    if file_size > max_audio_size:
        raise HTTPException(
            status_code=400,
            detail=f"Audio file too large. Maximum size: {max_audio_size} bytes"
        )
    
    try:
        # Save uploaded audio temporarily
        file_id = str(uuid.uuid4())
        temp_filename = f"{file_id}{file_extension}"
        temp_path = os.path.join(settings.UPLOAD_DIR, temp_filename)
        
        # Ensure upload directory exists
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
        
        # Save file
        async with aiofiles.open(temp_path, 'wb') as temp_file:
            content = await audio.read()
            await temp_file.write(content)
        
        # TODO: Implement actual speech recognition
        # This would use OpenAI Whisper or similar service
        
        # Placeholder response
        transcribed_text = "Bu bir örnek transkripsiyon metnidir."
        confidence_score = 0.85
        
        # Clean up temporary file
        try:
            os.remove(temp_path)
        except:
            pass
        
        return {
            "transcribed_text": transcribed_text,
            "confidence_score": confidence_score,
            "language": language,
            "duration_seconds": 0,  # TODO: Calculate actual duration
            "metadata": {
                "original_filename": audio.filename,
                "file_size": audio.size or 0
            }
        }
        
    except Exception as e:
        # Clean up temporary file on error
        try:
            if 'temp_path' in locals():
                os.remove(temp_path)
        except:
            pass
        
        raise HTTPException(status_code=500, detail=f"Speech transcription failed: {str(e)}")

@router.post("/pronunciation-score")
async def score_pronunciation(
    reference_text: str,
    audio: UploadFile = File(...),
    language: str = "tr"
):
    """Score pronunciation accuracy against reference text"""
    
    # Validate inputs
    if not reference_text.strip():
        raise HTTPException(status_code=400, detail="Reference text is required")
    
    # Validate file type
    allowed_audio_types = ['.mp3', '.wav', '.m4a', '.ogg', '.flac']
    filename = audio.filename or "unknown"
    file_extension = os.path.splitext(filename)[1].lower()
    
    if file_extension not in allowed_audio_types:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported audio format. Allowed: {allowed_audio_types}"
        )
    
    try:
        # Save uploaded audio temporarily
        file_id = str(uuid.uuid4())
        temp_filename = f"{file_id}{file_extension}"
        temp_path = os.path.join(settings.UPLOAD_DIR, temp_filename)
        
        # Ensure upload directory exists
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
        
        # Save file
        async with aiofiles.open(temp_path, 'wb') as temp_file:
            content = await audio.read()
            await temp_file.write(content)
        
        # TODO: Implement actual pronunciation scoring
        # This would compare the audio against reference text
        
        # Placeholder response
        overall_score = 78.5
        word_scores = []
        
        words = reference_text.split()
        for i, word in enumerate(words[:10]):  # Limit to first 10 words
            word_scores.append({
                "word": word,
                "score": 75 + (i % 20),  # Placeholder scores
                "feedback": "Good pronunciation" if (75 + (i % 20)) > 80 else "Needs improvement"
            })
        
        # Clean up temporary file
        try:
            os.remove(temp_path)
        except:
            pass
        
        return {
            "overall_score": overall_score,
            "word_scores": word_scores,
            "reference_text": reference_text,
            "feedback": "Good overall pronunciation. Focus on vowel clarity." if overall_score > 75 else "Practice more for better pronunciation.",
            "metadata": {
                "original_filename": audio.filename,
                "file_size": audio.size or 0,
                "language": language
            }
        }
        
    except Exception as e:
        # Clean up temporary file on error
        try:
            if 'temp_path' in locals():
                os.remove(temp_path)
        except:
            pass
        
        raise HTTPException(status_code=500, detail=f"Pronunciation scoring failed: {str(e)}")

@router.get("/supported-audio-formats")
async def get_supported_audio_formats():
    """Get list of supported audio formats"""
    
    return {
        "formats": [
            {
                "extension": ".mp3",
                "type": "MP3 Audio",
                "description": "MPEG Audio Layer 3"
            },
            {
                "extension": ".wav",
                "type": "WAV Audio", 
                "description": "Waveform Audio File Format"
            },
            {
                "extension": ".m4a",
                "type": "M4A Audio",
                "description": "MPEG-4 Audio"
            },
            {
                "extension": ".ogg",
                "type": "OGG Audio",
                "description": "Ogg Vorbis Audio"
            },
            {
                "extension": ".flac",
                "type": "FLAC Audio",
                "description": "Free Lossless Audio Codec"
            }
        ],
        "max_file_size": 25 * 1024 * 1024,
        "max_file_size_mb": 25
    }
