# Database Configuration
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/turkish_learning_app
POSTGRES_DB=turkish_learning_app
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# API Configuration
PORT=3001
NODE_ENV=development

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001

# AI/ML Configuration
OPENAI_API_KEY=your-openai-api-key-here
HUGGING_FACE_API_KEY=your-hugging-face-api-key-here

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./shared/uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Security Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3002
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Monitoring and Logging
LOG_LEVEL=debug
SENTRY_DSN=your-sentry-dsn-for-error-tracking
