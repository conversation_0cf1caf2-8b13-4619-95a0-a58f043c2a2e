"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPreferences = void 0;
const typeorm_1 = require("typeorm");
const types_1 = require("../../../../shared/types");
const user_entity_1 = require("./user.entity");
let UserPreferences = class UserPreferences {
};
exports.UserPreferences = UserPreferences;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserPreferences.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], UserPreferences.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'en' }),
    __metadata("design:type", String)
], UserPreferences.prototype, "language", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'light' }),
    __metadata("design:type", String)
], UserPreferences.prototype, "theme", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'email_notifications', default: true }),
    __metadata("design:type", Boolean)
], UserPreferences.prototype, "emailNotifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'push_notifications', default: true }),
    __metadata("design:type", Boolean)
], UserPreferences.prototype, "pushNotifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reminder_notifications', default: true }),
    __metadata("design:type", Boolean)
], UserPreferences.prototype, "reminderNotifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'achievement_notifications', default: true }),
    __metadata("design:type", Boolean)
], UserPreferences.prototype, "achievementNotifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'daily_xp_goal', default: 50 }),
    __metadata("design:type", Number)
], UserPreferences.prototype, "dailyXpGoal", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'weekly_lesson_goal', default: 5 }),
    __metadata("design:type", Number)
], UserPreferences.prototype, "weeklyLessonGoal", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'target_level',
        type: 'enum',
        enum: types_1.CEFRLevel,
        default: types_1.CEFRLevel.B2,
    }),
    __metadata("design:type", String)
], UserPreferences.prototype, "targetLevel", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], UserPreferences.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], UserPreferences.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => user_entity_1.User, (user) => user.preferences),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], UserPreferences.prototype, "user", void 0);
exports.UserPreferences = UserPreferences = __decorate([
    (0, typeorm_1.Entity)('user_preferences')
], UserPreferences);
//# sourceMappingURL=user-preferences.entity.js.map