import { UsersService } from './users.service';
import { UpdateUserDto, UpdatePreferencesDto } from './dto/update-user.dto';
export declare class UsersController {
    private usersService;
    constructor(usersService: UsersService);
    getProfile(req: any): Promise<import("./entities/user.entity").User>;
    updateProfile(req: any, updateUserDto: UpdateUserDto): Promise<import("./entities/user.entity").User>;
    getPreferences(req: any): Promise<import("./entities/user-preferences.entity").UserPreferences>;
    updatePreferences(req: any, updatePreferencesDto: UpdatePreferencesDto): Promise<import("./entities/user-preferences.entity").UserPreferences>;
    getStats(req: any): Promise<{
        user: {
            id: string;
            username: string;
            level: import("@/shared/types").CEFRLevel;
            xp: number;
            streak: number;
        };
        stats: {
            completedLessons: number;
            totalTimeSpent: number;
            achievementsCount: number;
            averageScore: number;
        };
    }>;
    deactivateAccount(req: any): Promise<{
        message: string;
    }>;
}
