version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: turkish-app-db
    environment:
      POSTGRES_DB: turkish_learning_app
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - turkish-app-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: turkish-app-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - turkish-app-network

  # Backend API (NestJS)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: turkish-app-backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=***********************************************/turkish_learning_app
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - PORT=3001
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - turkish-app-network
    command: npm run start:dev

  # Frontend Web App (Next.js)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: turkish-app-frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - turkish-app-network
    command: npm run dev

  # AI/NLP Microservices (Python)
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    container_name: turkish-app-ai
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=***********************************************/turkish_learning_app
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    ports:
      - "8000:8000"
    volumes:
      - ./ai-service:/app
      - ./shared/uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - turkish-app-network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Admin Panel (Next.js)
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: turkish-app-admin
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    ports:
      - "3002:3000"
    volumes:
      - ./admin:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - turkish-app-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  turkish-app-network:
    driver: bridge
