import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UserPreferences } from './entities/user-preferences.entity';
export declare class UsersService {
    private userRepository;
    private preferencesRepository;
    constructor(userRepository: Repository<User>, preferencesRepository: Repository<UserPreferences>);
    findById(id: string): Promise<User>;
    updateProfile(userId: string, updateData: Partial<User>): Promise<User>;
    getUserPreferences(userId: string): Promise<UserPreferences>;
    updatePreferences(userId: string, updateData: Partial<UserPreferences>): Promise<UserPreferences>;
    getUserStats(userId: string): Promise<{
        user: {
            id: string;
            username: string;
            level: import("@/shared/types").CEFRLevel;
            xp: number;
            streak: number;
        };
        stats: {
            completedLessons: number;
            totalTimeSpent: number;
            achievementsCount: number;
            averageScore: number;
        };
    }>;
    private calculateAverageScore;
    deactivateAccount(userId: string): Promise<void>;
}
