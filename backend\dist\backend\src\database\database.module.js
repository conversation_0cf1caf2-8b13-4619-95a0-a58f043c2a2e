"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../users/entities/user.entity");
const user_preferences_entity_1 = require("../users/entities/user-preferences.entity");
const course_entity_1 = require("../courses/entities/course.entity");
const unit_entity_1 = require("../courses/entities/unit.entity");
const lesson_entity_1 = require("../lessons/entities/lesson.entity");
const exercise_entity_1 = require("../lessons/entities/exercise.entity");
const vocabulary_item_entity_1 = require("../lessons/entities/vocabulary-item.entity");
const grammar_rule_entity_1 = require("../lessons/entities/grammar-rule.entity");
const user_progress_entity_1 = require("../progress/entities/user-progress.entity");
const exercise_attempt_entity_1 = require("../progress/entities/exercise-attempt.entity");
const achievement_entity_1 = require("../gamification/entities/achievement.entity");
const user_achievement_entity_1 = require("../gamification/entities/user-achievement.entity");
const review_item_entity_1 = require("../progress/entities/review-item.entity");
const file_upload_entity_1 = require("../common/entities/file-upload.entity");
const notification_entity_1 = require("../common/entities/notification.entity");
const user_session_entity_1 = require("../auth/entities/user-session.entity");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    type: 'postgres',
                    url: configService.get('DATABASE_URL'),
                    entities: [
                        user_entity_1.User,
                        user_preferences_entity_1.UserPreferences,
                        course_entity_1.Course,
                        unit_entity_1.Unit,
                        lesson_entity_1.Lesson,
                        exercise_entity_1.Exercise,
                        vocabulary_item_entity_1.VocabularyItem,
                        grammar_rule_entity_1.GrammarRule,
                        user_progress_entity_1.UserProgress,
                        exercise_attempt_entity_1.ExerciseAttempt,
                        achievement_entity_1.Achievement,
                        user_achievement_entity_1.UserAchievement,
                        review_item_entity_1.ReviewItem,
                        file_upload_entity_1.FileUpload,
                        notification_entity_1.Notification,
                        user_session_entity_1.UserSession,
                    ],
                    synchronize: process.env.NODE_ENV === 'development',
                    logging: process.env.NODE_ENV === 'development',
                    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
                }),
                inject: [config_1.ConfigService],
            }),
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map