"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./entities/user.entity");
const user_preferences_entity_1 = require("./entities/user-preferences.entity");
let UsersService = class UsersService {
    constructor(userRepository, preferencesRepository) {
        this.userRepository = userRepository;
        this.preferencesRepository = preferencesRepository;
    }
    async findById(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['preferences'],
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async updateProfile(userId, updateData) {
        await this.userRepository.update(userId, updateData);
        return this.findById(userId);
    }
    async getUserPreferences(userId) {
        const preferences = await this.preferencesRepository.findOne({
            where: { userId },
        });
        if (!preferences) {
            const newPreferences = this.preferencesRepository.create({ userId });
            return this.preferencesRepository.save(newPreferences);
        }
        return preferences;
    }
    async updatePreferences(userId, updateData) {
        await this.preferencesRepository.update({ userId }, updateData);
        return this.getUserPreferences(userId);
    }
    async getUserStats(userId) {
        const user = await this.userRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.progress', 'progress', 'progress.is_completed = true')
            .leftJoinAndSelect('user.achievements', 'achievements')
            .where('user.id = :userId', { userId })
            .getOne();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const completedLessons = user.progress?.length || 0;
        const totalTimeSpent = user.progress?.reduce((sum, p) => sum + (p.timeSpent || 0), 0) || 0;
        const achievementsCount = user.achievements?.length || 0;
        return {
            user: {
                id: user.id,
                username: user.username,
                level: user.level,
                xp: user.xp,
                streak: user.streak,
            },
            stats: {
                completedLessons,
                totalTimeSpent,
                achievementsCount,
                averageScore: this.calculateAverageScore(user.progress),
            },
        };
    }
    calculateAverageScore(progress) {
        if (!progress || progress.length === 0)
            return 0;
        const totalScore = progress.reduce((sum, p) => sum + (p.score || 0), 0);
        return Math.round(totalScore / progress.length);
    }
    async deactivateAccount(userId) {
        await this.userRepository.update(userId, { isActive: false });
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(user_preferences_entity_1.UserPreferences)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], UsersService);
//# sourceMappingURL=users.service.js.map